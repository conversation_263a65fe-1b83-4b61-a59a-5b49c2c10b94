<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业文化符号提炼器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        input, button {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        #company-name {
            width: 70%;
        }
        #start-button {
            width: 25%;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        #start-button:hover {
            background-color: #0056b3;
        }
        #chat-input {
            width: 85%;
        }
        #send-button {
            width: 12%;
            background-color: #28a745;
            color: white;
            cursor: pointer;
        }
        #send-button:hover {
            background-color: #218838;
        }
        #status {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7ef;
            border-radius: 5px;
        }
        .proposal {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .hidden {
            display: none;
        }
        #chat-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
        }
        .input-area {
            display: flex;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>企业文化符号提炼器</h1>
        <div id="input-section">
            <input type="text" id="company-name" placeholder="请输入企业名称">
            <button id="start-button" onclick="startProcess()">开始分析</button>
        </div>
        <div id="status" class="hidden"></div>
        <div id="chat-container" class="hidden">
            <div id="messages"></div>
            <div class="input-area">
                <input type="text" id="chat-input" placeholder="输入您的问题...">
                <button id="send-button" onclick="sendMessage()">发送</button>
            </div>
        </div>
        <div id="results" class="hidden">
            <div class="result">
                <h2>企业信息总结</h2>
                <p id="summary"></p>
            </div>
            <div id="proposals">
                <h2>文化符号提炼方案</h2>
                <div id="proposal-1" class="proposal"></div>
                <div id="proposal-2" class="proposal"></div>
                <div id="proposal-3" class="proposal"></div>
            </div>
            <button onclick="startNewSession()">开始新会话</button>
        </div>
    </div>

    <script>
        const socket = io();
        let sessionId = null;
        
        function startProcess() {
            const companyName = document.getElementById('company-name').value;
            if (!companyName) {
                alert('请输入企业名称');
                return;
            }
            
            // 隐藏输入部分，显示聊天界面
            document.getElementById('input-section').classList.add('hidden');
            document.getElementById('status').classList.remove('hidden');
            document.getElementById('status').innerText = '正在连接...';
            
            // 发送开始处理请求
            socket.emit('start_process', {company_name: companyName});
        }
        
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (!message) return;
            
            // 显示用户消息
            addMessage(message, 'user');
            input.value = '';
            
            // 发送消息到服务器
            socket.emit('chat_message', {session_id: sessionId, message: message});
        }
        
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.innerText = text;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function startNewSession() {
            // 重置界面
            document.getElementById('input-section').classList.remove('hidden');
            document.getElementById('chat-container').classList.add('hidden');
            document.getElementById('results').classList.add('hidden');
            document.getElementById('messages').innerHTML = '';
            document.getElementById('company-name').value = '';
            sessionId = null;
        }
        
        // 回车发送消息
        document.getElementById('chat-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        socket.on('connect', function() {
            console.log('Connected to server');
        });
        
        socket.on('status_update', function(data) {
            document.getElementById('status').innerText = data.message;
        });
        
        socket.on('process_started', function(data) {
            sessionId = data.session_id;
            document.getElementById('status').classList.add('hidden');
            document.getElementById('chat-container').classList.remove('hidden');
            addMessage('您好！我已经开始分析' + data.company_name + '的企业文化。请问您有什么问题或需要我重点关注的方面吗？', 'assistant');
        });
        
        socket.on('process_completed', function(data) {
            // 隐藏聊天界面，显示结果
            document.getElementById('chat-container').classList.add('hidden');
            document.getElementById('results').classList.remove('hidden');
            
            // 填充总结
            document.getElementById('summary').innerText = data.summary;
            
            // 填充方案
            for (let i = 0; i < 3; i++) {
                if (data.proposals[i]) {
                    document.getElementById(`proposal-${i+1}`).innerHTML = `<h3>方案 ${i+1}</h3><p>${data.proposals[i]}</p>`;
                }
            }
        });
        
        socket.on('chat_response', function(data) {
            addMessage(data.response, 'assistant');
        });
    </script>
</body>
</html>